import { type Page, type Locator, expect } from '@playwright/test';
import type { TestStep, TestResult, StepResult, PageEx } from '../../types';
import type { ExecutionContext } from '../../testing-engine/execution-engine';
import { logger } from '../../utils/logger';
import { retry, formatDuration, prepareScriptForEvaluate } from '../../utils/helpers';
import { FilePathResolver } from '../../utils/file-path-resolver';
import { DSLParser } from '../../dsl/parser';

export interface ExecutionOptions {
  timeout?: number;
  retries?: number;
  continueOnFailure?: boolean;
  captureScreenshots?: boolean;
}

/**
 * 需要唯一元素的 Action 类型
 */
const SINGLE_ELEMENT_ACTIONS = new Set([
  'click', 'dblclick', 'rightClick', 'hover',
  'fill', 'selectOption', 'selectDate', 'check', 'uncheck', 'setInputFiles',
  'extractText', 'extractAttribute'
]);

/**
 * 元素选择器错误，包含详细的步骤信息
 */
export class ElementSelectorError extends Error {
  constructor(
    message: string,
    public readonly step: TestStep,
    public readonly foundCount: number,
    public readonly selector: string
  ) {
    super(message);
    this.name = 'ElementSelectorError';
  }
}

/**
 * Web平台执行器
 * 处理标准的Web操作，作为其他执行器的基础类
 */
export class WebExecutor {
  constructor() {
    // 构造函数现在很简单，脚本系统通过 context 注入
  }

  /**
   * 获取单个元素，如果找到多个元素则抛出错误
   * 支持 within 上下文定位
   */
  protected async requireSingleElement(
    step: TestStep,
    page: Page,
    actionName?: string
  ): Promise<Locator> {
    // 1. 首先确定搜索上下文（容器）
    const searchContext = await this.getSearchContext(step, page);

    // 2. 在确定的上下文中查找目标元素
    return this.findElementInContext(step, searchContext, actionName);
  }

  /**
   * 获取搜索上下文（容器）
   * 如果指定了 within，则返回对应的容器元素；否则返回整个页面
   */
  private async getSearchContext(step: TestStep, page: Page): Promise<Locator | Page> {
    // 如果没有指定 within，使用整个页面
    if (!step.within) {
      return page;
    }

    // 复用现有逻辑：将 within 当作一个元素定位任务
    const contextStep: TestStep = {
      id: 'context-locator',
      name: 'context locator',
      action: 'click', // 使用一个有效的 action 类型
      role: step.within.role,
      roleOptions: step.within.roleOptions,
      selector: step.within.selector
    };

    // 直接复用现有的定位逻辑！
    return this.findElementInContext(contextStep, page, 'context');
  }

  /**
   * 在指定上下文中查找元素
   * 完全复用现有的定位逻辑
   */
  private async findElementInContext(
    step: TestStep,
    context: Locator | Page,
    actionName?: string
  ): Promise<Locator> {
    let locator: Locator;
    let locatorDescription: string;
    const action = actionName || step.action || 'unknown';

    // 完全复用现有的定位逻辑
    if (step.role) {
      const options: any = {};
      if (step.roleOptions) {
        Object.assign(options, step.roleOptions);
      }

      // 关键：在指定上下文中查找
      locator = context.getByRole(step.role as any, options);

      const optionsDesc = step.roleOptions?.name ? ` name="${step.roleOptions.name}"` : '';
      locatorDescription = `role=${step.role}${optionsDesc}`;
    }
    else if (step.selector) {
      // 关键：在指定上下文中查找
      locator = context.locator(step.selector);
      locatorDescription = `selector="${step.selector}"`;
    }
    else {
      throw new ElementSelectorError(
        `${action} action requires either 'role' or 'selector' field`,
        step, 0, ''
      );
    }

    // 复用现有的验证逻辑
    const count = await locator.count();

    if (count === 0) {
      const isPageContext = 'url' in context; // 判断是否为 Page 对象
      const contextDesc = isPageContext ? 'page' : 'within context';
      throw new ElementSelectorError(
        `No elements found for ${locatorDescription} in ${contextDesc} for ${action} action`,
        step, count, locatorDescription
      );
    }

    if (count > 1) {
      const elementDescriptions = await this.getElementDescriptions(locator, Math.min(count, 3));
      const isPageContext = 'url' in context; // 判断是否为 Page 对象
      const contextDesc = isPageContext ? 'page' : 'within context';

      throw new ElementSelectorError(
        `Multiple elements (${count}) found for ${locatorDescription} in ${contextDesc} for ${action} action.\n` +
        `Found elements:\n${elementDescriptions}\n` +
        `Step details: ${JSON.stringify(step, null, 2)}\n` +
        `Suggestions:\n` +
        `- Use a more specific ${step.role ? 'role with name' : 'selector'} (e.g., ${step.role ? 'add exact name match' : 'add ID, class, or attribute'})\n` +
        `- Consider using 'within' to specify a container context:\n` +
        `  Example with role:\n` +
        `    within:\n` +
        `      role: dialog\n` +
        `      roleOptions:\n` +
        `        name: "Dialog Title"\n` +
        `  Example with selector:\n` +
        `    within:\n` +
        `      selector: '[role="dialog"]'\n` +
        `- Consider using role-based locators for better stability\n`,
        step, count, locatorDescription
      );
    }

    return locator;
  }

  /**
   * 获取元素的描述信息用于错误报告
   */
  private async getElementDescriptions(locator: Locator, maxCount: number = 3): Promise<string> {
    const descriptions: string[] = [];

    for (let i = 0; i < maxCount; i++) {
      try {
        const element = locator.nth(i);
        const tagName = await element.evaluate(el => el.tagName.toLowerCase());
        const id = await element.getAttribute('id');
        const className = await element.getAttribute('class');
        const text = await element.textContent();

        let desc = `  ${i + 1}. <${tagName}`;
        if (id) desc += ` id="${id}"`;
        if (className) desc += ` class="${className.slice(0, 50)}${className.length > 50 ? '...' : ''}"`;
        desc += '>';
        if (text && text.trim()) {
          desc += ` "${text.slice(0, 30)}${text.length > 30 ? '...' : ''}"`;
        }

        descriptions.push(desc);
      } catch (error) {
        descriptions.push(`  ${i + 1}. <element description failed>`);
      }
    }

    return descriptions.join('\n');
  }

  /**
   * 检查 action 是否需要单个元素
   */
  protected requiresSingleElement(actionName: string): boolean {
    return SINGLE_ELEMENT_ACTIONS.has(actionName);
  }

  /**
   * Execute a test with the given steps
   */
  async executeTest(
    testId: string,
    steps: TestStep[],
    context: ExecutionContext,
    options: ExecutionOptions = {}
  ): Promise<TestResult> {
    const startTime = Date.now();
    const result: TestResult = {
      id: testId,
      name: testId,
      status: 'passed',
      duration: 0,
      steps: [],
    };

    logger.info('Starting test execution', {
      testId,
      stepCount: steps.length,
      options,
    });

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        logger.debug('Executing step', {
          stepIndex: i + 1,
          stepId: step.id,
          action: step.action,
        });

        const stepResult = await this.executeStep(step, context, undefined, options);
        result.steps.push(stepResult);

        if (stepResult.status === 'failed') {
          result.status = 'failed';
          result.error = stepResult.error;

          if (!options.continueOnFailure) {
            logger.error('Test execution stopped due to step failure', {
              testId,
              stepId: step.id,
              error: stepResult.error,
            });
            break;
          }
        }
      }
    } catch (error) {
      result.status = 'failed';
      result.error = error instanceof Error ? error.message : String(error);
      logger.error('Test execution failed with unexpected error', {
        testId,
        error: result.error,
      });
    }

    result.duration = Date.now() - startTime;

    logger.info('Test execution completed', {
      testId,
      status: result.status,
      duration: formatDuration(result.duration),
      stepCount: result.steps.length,
    });

    return result;
  }



  /**
   * Execute a single test step
   */
  async executeStep(
    step: TestStep,
    context: ExecutionContext,
    templateParameters?: Record<string, any>,
    options: ExecutionOptions = {}
  ): Promise<StepResult> {
    const startTime = Date.now();
    const result: StepResult = {
      id: step.id,
      name: step.name,
      status: 'passed',
      duration: 0,
    };

    logger.debug('Executing step', {
      stepId: step.id,
      action: step.action,
      selector: step.selector,
    });

    try {
      // 1. 先解析变量（包括 when 条件）
      const resolvedStep = context.dataAccessor.resolveObject(step);

      // 2. 检查步骤级条件（在验证之前）
      // 使用原始的未解析条件，避免字符串解析问题
      if (step.when && !this.evaluateCondition(step.when, context, templateParameters)) {
        logger.debug('步骤条件不满足，跳过执行', {
          stepId: step.id,
          condition: step.when,
          resolvedCondition: resolvedStep.when
        });
        // 返回成功状态，表示步骤被正确跳过
        result.status = 'passed';
        result.duration = Date.now() - startTime;
        return result;
      }

      // 3. 验证步骤格式（只对需要执行的步骤进行验证）
      this.validateStepFormat(resolvedStep);

      // 4. 执行步骤
      const retries = step.retry?.attempts || options.retries || 1;
      const retryDelay = step.retry?.delay || 1000;

      await retry(() => this.executeStepAction(resolvedStep, context), {
        maxAttempts: retries,
        delay: retryDelay,
      });

      // Extract data if needed
      if (this.isDataExtractionStep(step)) {
        result.data = await this.extractStepData(resolvedStep, context);
      }

      logger.debug('Step executed successfully', {
        stepId: step.id,
        action: step.action,
        duration: Date.now() - startTime,
      });
    } catch (error) {
      debugger;
      result.status = 'failed';
      result.error = error instanceof Error ? error.message : String(error);
      logger.error('Step execution failed', {
        stepId: step.id,
        action: step.action,
        error: result.error,
      });

      // Capture screenshot on failure if enabled
      if (options.captureScreenshots) {
        try {
          const screenshotPath = `screenshot-${step.id}-${Date.now()}.png`;
          await context.page.screenshot({ path: screenshotPath });
          logger.info('Screenshot captured', { path: screenshotPath });
        } catch (screenshotError) {
          logger.warn('Failed to capture screenshot', {
            error: screenshotError instanceof Error ? screenshotError.message : String(screenshotError),
          });
        }
      }
    }

    result.duration = Date.now() - startTime;
    return result;
  }

  /**
   * Execute the action for a test step
   */
  async executeStepAction(step: TestStep, context: ExecutionContext): Promise<void> {
    const page = context.page;

    if (!step.action) {
      throw new Error('Step action is required');
    }

    logger.debug('Executing step action', {
      stepId: step.id,
      action: step.action,
      selector: step.selector,
    });

    switch (step.action) {
      // Navigation actions
      case 'navigate':
        await this.executeNavigate(step, page as PageEx, context);
        break;
      case 'goBack':
        await page.goBack();
        break;
      case 'goForward':
        await page.goForward();
        break;
      case 'reload':
        await page.reload();
        break;

      // Element interaction actions
      case 'click':
        await this.executeClick(step, page, context);
        break;
      case 'dblclick':
        await this.executeDblClick(step, page);
        break;
      case 'rightClick':
        await this.executeRightClick(step, page);
        break;
      case 'hover':
        await this.executeHover(step, page);
        break;

      // Form actions
      case 'fill':
        await this.executeFill(step, page, context);
        break;
      case 'selectOption':
        await this.executeSelectOption(step, page);
        break;
      case 'selectDate':
        await this.executeSelectDate(step, page);
        break;
      case 'check':
        await this.executeCheck(step, page);
        break;
      case 'uncheck':
        await this.executeUncheck(step, page);
        break;
      case 'setInputFiles':
        await this.executeSetInputFiles(step, page);
        break;

      // Wait actions
      case 'wait':
        await this.executeWait(step, page);
        break;
      case 'waitForSelector':
        await this.executeWaitForSelector(step, page, context);
        break;
      case 'waitForText':
        await this.executeWaitForText(step, page);
        break;
      case 'waitForResponse':
        await this.executeWaitForResponse(step, page);
        break;
      case 'waitForLoadState':
        await this.executeWaitForLoadState(step, page);
        break;

      // Verification actions
      case 'verify':
        await this.executeVerify(step, page, context);
        break;

      // Data extraction actions
      case 'extract':
        await this.extractStepData(step, context);
        break;
      case 'extractText':
        await this.executeExtractText(step, page, context);
        break;
      case 'extractAttribute':
        await this.executeExtractAttribute(step, page, context);
        break;

      // Variable actions
      case 'setVariable':
        await this.executeSetVariable(step, context);
        break;

      // Template actions
      case 'useTemplate':
        await this.executeUseTemplate(step, page, context);
        break;

      // Control flow actions
      case 'condition':
        await this.executeCondition(step, page, context);
        break;

      // 向后兼容
      case 'conditional':
        await this.executeCondition(step, page, context);
        break;

      case 'forEach':
        await this.executeForEach(step, page, context);
        break;

      // Utility actions
      case 'screenshot':
        await this.executeScreenshot(step, page);
        break;

      // Script execution
      case 'executeScript':
        await this.executeScript(step, page, context);
        break;

      // Use registered script
      case 'useScript':
        await this.executeUseScript(step, page, context);
        break;

      default:
        throw new Error(`Unsupported action: ${step.action}`);
    }
  }

  /**
   * Validate step format
   */
  protected validateStepFormat(step: TestStep): void {
    if (!step.id) {
      throw new Error('Step id is required');
    }
    if (!step.name) {
      throw new Error('Step name is required');
    }
    if (!step.action) {
      throw new Error('Step action is required');
    }
  }

  /**
   * Check if step is a data extraction step
   */
  protected isDataExtractionStep(step: TestStep): boolean {
    return ['extract', 'extractText', 'extractAttribute'].includes(step.action || '');
  }
  /**
   * Extract data from step
   */
  protected async extractStepData(step: TestStep, context: ExecutionContext): Promise<any> {
    // 简化实现，实际需要从原StrictExecutor复制
    return null;
  }

  /**
   * Navigation action implementations
   */
  protected async executeNavigate(step: TestStep, page: PageEx, context: ExecutionContext): Promise<void> {
    const url = step.url || step.data;
    if (!url) {
      throw new Error('Navigate action requires url or data field');
    }

    // 是否是完整 url
    const isFullUrl = url.startsWith('http');

    const navigateUrl = isFullUrl ? url : (context.config.baseUrl + url).replace(/\/+/g, '/');
    
    const options = step.options || {};
    await page.goto(navigateUrl, {
      waitUntil: options.waitUntil || 'load',
      timeout: options.timeout,
    });
  }

  /**
   * Click action implementations
   */
  protected async executeClick(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'click');
    const options = step.options || {};

    if (options.hover) {
      await element.hover({ force: true });
    }

    await element.click({
      timeout: options.timeout,
      force: true,
      position: options.position,
    });

    if (step.script) {
      logger.debug('Executing post-click script', {
        selector: step.selector,
        script: step.script
      });
      await this.executeInlineScript(step.script, page, context, step);
    }
  }

  protected async executeDblClick(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'dblclick');
    await element.dblclick({ force: true });
  }

  protected async executeRightClick(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'rightClick');
    await element.click({ button: 'right', force: true });
  }

  protected async executeHover(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'hover');
    await element.hover({ force: true });
  }

  /**
   * Form action implementations
   */
  protected async executeFill(step: TestStep, page: Page, _context: ExecutionContext): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'fill');
    const value = step.value || step.data || '';
    const options = step.options || {};

    if (options.clear) {
      await element.clear();
    }

    await element.fill(String(value), {
      timeout: options.timeout,
      force: options.force,
    });
  }

  protected async executeSelectOption(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'selectOption');
    const value = step.value || step.data;

    if (step.label) {
      await element.selectOption({ label: step.label as string });
    } else if (step.index !== undefined) {
      await element.selectOption({ index: Number(step.index) });
    } else if (value !== undefined) {
      await element.selectOption({ value: String(value) });
    } else {
      throw new Error('SelectOption action requires value, label, or index');
    }
  }

  protected async executeSelectDate(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'selectDate');
    await element.evaluate((el: any) => {
      el.value = step.value;
    });
  }

  protected async executeCheck(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'check');
    await element.check({force: true});
  }

  protected async executeUncheck(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'uncheck');
    await element.uncheck({force: true});
  }

  protected async executeSetInputFiles(step: TestStep, page: Page): Promise<void> {
    const element = await this.requireSingleElement(step, page, 'setInputFiles');
    const files = step.files || step.data;
    if (!files) {
      throw new Error('SetInputFiles requires files field');
    }

    try {
      // 将文件名解析为完整路径
      const filePaths = Array.isArray(files)
        ? FilePathResolver.resolveMultipleFilePaths(files)
        : [FilePathResolver.resolveFilePath(files)];

      logger.debug('文件上传', {
        stepId: step.id,
        originalFiles: files,
        resolvedPaths: filePaths
      });

      await element.setInputFiles(filePaths);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('文件上传失败', {
        stepId: step.id,
        files,
        error: errorMessage
      });
      throw new Error(`文件上传失败: ${errorMessage}`);
    }
  }

  /**
   * Wait action implementations
   */
  protected async executeWait(step: TestStep, page: Page): Promise<void> {
    const duration = step.duration || step.data || 1000;
    await page.waitForTimeout(Number(duration));
  }

  protected async executeWaitForSelector(step: TestStep, page: Page, _context: ExecutionContext): Promise<void> {
    if (!step.selector) {
      throw new Error('WaitForSelector action requires selector field');
    }

    const options = step.options || {};
    const state = step.state || 'visible';

    await page.waitForSelector(step.selector, {
      state: state as any,
      timeout: options.timeout,
    });
  }

  protected async executeWaitForText(step: TestStep, page: Page): Promise<void> {
    const text = step.data || step.value;
    if (!text) {
      throw new Error('WaitForText action requires text data');
    }
    await page.waitForFunction(
      // @ts-ignore
      (text) => document.body.textContent?.includes(text),
      text
    );
  }

  protected async executeWaitForResponse(step: TestStep, page: Page): Promise<void> {
    const url = step.url || step.data;
    if (!url) {
      throw new Error('WaitForResponse action requires url');
    }
    await page.waitForResponse(url);
  }

  protected async executeWaitForLoadState(step: TestStep, page: Page): Promise<void> {
    const state = step.state || 'load';
    await page.waitForLoadState(state as any);
  }

  /**
   * Utility methods
   */
  protected async executeInlineScript(
    script: string,
    page: Page,
    context: ExecutionContext,
    _step: TestStep
  ): Promise<any> {
    // 准备上下文变量
    const scriptContext = {
      // 注入当前环境变量
      ...context.dataAccessor.getAllVariables(),
      // 注入配置信息
      config: context.dataAccessor.getAllConfig(),
    };

    const wrappedScript = prepareScriptForEvaluate(script, scriptContext);
    return await page.evaluate(wrappedScript);
  }

  protected async executeScreenshot(step: TestStep, page: Page): Promise<void> {
    const options = step.options || {};
    const filename = step.data || `screenshot-${Date.now()}.png`;

    await page.screenshot({
      path: filename,
      fullPage: options.fullPage,
      quality: options.quality,
    });
  }

  protected async executeScript(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    if (!step.script) {
      throw new Error('ExecuteScript action requires script field');
    }

    // 准备上下文变量
    const scriptContext = {
      // 注入当前环境变量
      ...context.dataAccessor.getAllVariables(),
      // 注入配置信息
      config: context.dataAccessor.getAllConfig(),
    };

    const wrappedScript = prepareScriptForEvaluate(step.script, scriptContext);
    await page.evaluate(wrappedScript);
  }

  protected async executeUseScript(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {

    if (!step.script) {
      throw new Error('UseScript action requires script field');
    }

    if (!context.scriptManager) {
      throw new Error('脚本管理器未初始化，无法执行脚本');
    }

    logger.debug('执行注册脚本', {
      scriptName: step.script,
      parameters: step.parameters
    });

    try {
      // 构建脚本执行上下文
      const scriptContext = {
        page,
        logger,
        dataAccessor: context.dataAccessor,
        variables: {} // 可以从 context 中提取更多变量
      };

      // 执行注册的脚本
      const result = await context.scriptManager.executeScript(
        step.script,
        step.parameters || {},
        scriptContext
      );

      logger.debug('脚本执行完成', {
        scriptName: step.script,
        success: result.success,
        duration: result.duration
      });

      if (!result.success) {
        throw new Error(`脚本执行失败: ${result.error}`);
      }

      // 检查脚本返回结果的业务状态
      if (result.result && typeof result.result === 'object' && result.result.success === false) {
        throw new Error(`脚本业务逻辑失败: ${result.result.error || '未知业务错误'}`);
      }

      return result.result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('脚本执行异常', {
        scriptName: step.script,
        error: errorMessage
      });
      throw new Error(`UseScript action failed: ${errorMessage}`);
    }
  }

  // 验证功能的完整实现
  protected async executeVerify(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    const verifyType = step.type || 'element';

    switch (verifyType) {
      case 'element':
        await this.verifyElement(step, page, context);
        break;
      case 'text':
        await this.verifyText(step, page, context);
        break;
      case 'count':
        await this.verifyCount(step, page, context);
        break;
      case 'attribute':
        await this.verifyAttribute(step, page, context);
        break;
      case 'page':
        await this.verifyPage(step, page, context);
        break;
      case 'class':
        await this.verifyClass(step, page, context);
        break;
      default:
        throw new Error(`Unsupported verify type: ${verifyType}`);
    }
  }

  /**
   * 获取元素定位器 - 支持 role + roleOptions 和 selector 两种方式
   */
  private getElementLocator(step: TestStep, page: Page): Locator {
    if (step.role) {
      // 使用 role + roleOptions 定位（推荐方式）
      const roleOptions = step.roleOptions || {};
      return page.getByRole(step.role as any, roleOptions);
    } else if (step.selector) {
      // 使用 CSS 选择器定位（备选方式）
      return page.locator(step.selector);
    } else {
      throw new Error('Element verification requires either role or selector field');
    }
  }

  /**
   * 验证元素状态
   */
  private async verifyElement(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    const locator = this.getElementLocator(step, page);
    const assertion = step.assertion || 'visible';
    const timeout = step.timeout || 5000;

    switch (assertion) {
      case 'visible':
        await expect(locator).toBeVisible({ timeout });
        break;
      case 'hidden':
        await expect(locator).toBeHidden({ timeout });
        break;
      case 'enabled':
        await expect(locator).toBeEnabled({ timeout });
        break;
      case 'disabled':
        await expect(locator).toBeDisabled({ timeout });
        break;
      case 'checked':
        await expect(locator).toBeChecked({ timeout });
        break;
      case 'empty':
        await expect(locator).toBeEmpty({ timeout });
        break;
      case 'value':
        if (step.expected === undefined) {
          throw new Error('Element value verification requires expected field');
        }
        await expect(locator).toHaveValue(String(step.expected), { timeout });
        break;
      case 'text':
        if (step.expected === undefined) {
          throw new Error('Element text verification requires expected field');
        }
        await expect(locator).toHaveText(String(step.expected), { timeout });
        break;
      case 'count':
        if (step.expected === undefined) {
          throw new Error('Element count verification requires expected field');
        }
        await expect(locator).toHaveCount(Number(step.expected), { timeout });
        break;
      default:
        throw new Error(`Unsupported element assertion: ${assertion}`);
    }
  }

  /**
   * 验证文本内容
   */
  private async verifyText(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    if (!step.data) {
      throw new Error('Text verification requires data field');
    }

    const assertion = step.assertion || 'visible';
    const timeout = step.timeout || 5000;
    const textContent = String(step.data);
    const expected = step.expected ? String(step.expected) : '';

    switch (assertion) {
      case 'visible':
        await expect(page.getByText(textContent)).toBeVisible({ timeout });
        break;
      case 'hidden':
        await expect(page.getByText(textContent)).toBeHidden({ timeout });
        break;
      case 'equals':
        await expect(page.getByText(textContent)).toHaveText(expected, { timeout });
        break;
      case 'contains':
        await expect(page.getByText(textContent)).toContainText(expected, { timeout });
        break;
      case 'startsWith':
        await expect(page.getByText(textContent)).toHaveText(new RegExp(`^${this.escapeRegex(expected)}`), { timeout });
        break;
      case 'endsWith':
        await expect(page.getByText(textContent)).toHaveText(new RegExp(`${this.escapeRegex(expected)}$`), { timeout });
        break;
      case 'matches':
        await expect(page.getByText(textContent)).toHaveText(new RegExp(expected), { timeout });
        break;
      default:
        throw new Error(`Unsupported text assertion: ${assertion}`);
    }
  }

  /**
   * 验证元素数量
   */
  private async verifyCount(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    if (!step.selector) {
      throw new Error('Count verification requires selector field');
    }
    if (step.expected === undefined) {
      throw new Error('Count verification requires expected field');
    }

    const locator = page.locator(step.selector);
    const assertion = step.assertion || 'equals';
    const timeout = step.timeout || 5000;
    const expectedCount = Number(step.expected);

    switch (assertion) {
      case 'equals':
        await expect(locator).toHaveCount(expectedCount, { timeout });
        break;
      case 'greaterThan':
        // 使用自定义匹配器实现 greaterThan
        await expect.poll(async () => await locator.count(), { timeout }).toBeGreaterThan(expectedCount);
        break;
      case 'lessThan':
        // 使用自定义匹配器实现 lessThan
        await expect.poll(async () => await locator.count(), { timeout }).toBeLessThan(expectedCount);
        break;
      default:
        throw new Error(`Unsupported count assertion: ${assertion}`);
    }
  }

  /**
   * 验证元素属性
   */
  private async verifyAttribute(step: TestStep, page: Page, _context: ExecutionContext): Promise<void> {
    if (!step.selector) {
      throw new Error('Attribute verification requires selector field');
    }
    if (!step.property) {
      throw new Error('Attribute verification requires property field');
    }
    if (step.expected === undefined) {
      throw new Error('Attribute verification requires expected field');
    }

    const locator = page.locator(step.selector);
    const assertion = step.assertion || 'equals';
    const timeout = step.timeout || 5000;
    const attributeName = step.property;
    const expected = String(step.expected);

    switch (assertion) {
      case 'equals':
        await expect(locator).toHaveAttribute(attributeName, expected, { timeout });
        break;
      case 'contains':
        await expect(locator).toHaveAttribute(attributeName, new RegExp(this.escapeRegex(expected)), { timeout });
        break;
      case 'startsWith':
        await expect(locator).toHaveAttribute(attributeName, new RegExp(`^${this.escapeRegex(expected)}`), { timeout });
        break;
      case 'endsWith':
        await expect(locator).toHaveAttribute(attributeName, new RegExp(`${this.escapeRegex(expected)}$`), { timeout });
        break;
      case 'matches':
        await expect(locator).toHaveAttribute(attributeName, new RegExp(expected), { timeout });
        break;
      default:
        throw new Error(`Unsupported attribute assertion: ${assertion}`);
    }
  }

  /**
   * 验证页面属性
   */
  private async verifyPage(step: TestStep, page: Page, _context: ExecutionContext): Promise<void> {
    if (!step.property) {
      throw new Error('Page verification requires property field');
    }
    if (step.expected === undefined) {
      throw new Error('Page verification requires expected field');
    }

    const assertion = step.assertion || 'equals';
    const timeout = step.timeout || 5000;
    const expected = String(step.expected);

    switch (step.property) {
      case 'title':
        await this.verifyPageTitle(page, assertion, expected, timeout);
        break;
      case 'url':
        await this.verifyPageUrl(page, assertion, expected, timeout);
        break;
      default:
        throw new Error(`Unsupported page property: ${step.property}`);
    }
  }

  /**
   * 验证CSS类
   */
  private async verifyClass(step: TestStep, page: Page, _context: ExecutionContext): Promise<void> {
    if (!step.selector) {
      throw new Error('Class verification requires selector field');
    }
    if (step.expected === undefined) {
      throw new Error('Class verification requires expected field');
    }

    const locator = page.locator(step.selector);
    const assertion = step.assertion || 'contains';
    const timeout = step.timeout || 5000;
    const expectedClass = String(step.expected);

    switch (assertion) {
      case 'contains':
        await expect(locator).toHaveClass(new RegExp(this.escapeRegex(expectedClass)), { timeout });
        break;
      case 'equals':
        await expect(locator).toHaveClass(expectedClass, { timeout });
        break;
      default:
        throw new Error(`Unsupported class assertion: ${assertion}`);
    }
  }

  /**
   * 验证页面标题
   */
  private async verifyPageTitle(page: Page, assertion: string, expected: string, timeout: number): Promise<void> {
    switch (assertion) {
      case 'equals':
        await expect(page).toHaveTitle(expected, { timeout });
        break;
      case 'contains':
        await expect(page).toHaveTitle(new RegExp(this.escapeRegex(expected)), { timeout });
        break;
      case 'startsWith':
        await expect(page).toHaveTitle(new RegExp(`^${this.escapeRegex(expected)}`), { timeout });
        break;
      case 'endsWith':
        await expect(page).toHaveTitle(new RegExp(`${this.escapeRegex(expected)}$`), { timeout });
        break;
      case 'matches':
        await expect(page).toHaveTitle(new RegExp(expected), { timeout });
        break;
      default:
        throw new Error(`Unsupported page title assertion: ${assertion}`);
    }
  }

  /**
   * 验证页面URL
   */
  private async verifyPageUrl(page: Page, assertion: string, expected: string, timeout: number): Promise<void> {
    switch (assertion) {
      case 'equals':
        await expect(page).toHaveURL(expected, { timeout });
        break;
      case 'contains':
        await expect(page).toHaveURL(new RegExp(this.escapeRegex(expected)), { timeout });
        break;
      case 'startsWith':
        await expect(page).toHaveURL(new RegExp(`^${this.escapeRegex(expected)}`), { timeout });
        break;
      case 'endsWith':
        await expect(page).toHaveURL(new RegExp(`${this.escapeRegex(expected)}$`), { timeout });
        break;
      case 'matches':
        await expect(page).toHaveURL(new RegExp(expected), { timeout });
        break;
      default:
        throw new Error(`Unsupported page URL assertion: ${assertion}`);
    }
  }

  /**
   * 转义正则表达式特殊字符
   */
  private escapeRegex(str: string): string {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  // 数据提取方法
  protected async executeExtractText(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    if (!step.selector && !step.role) {
      throw new Error('extractText action requires selector or role field');
    }

    if (!step.variable) {
      throw new Error('extractText action requires variable field');
    }

    const element = await this.requireSingleElement(step, page, 'extractText');
    const text = await element.textContent();

    // 设置为全局变量
    context.dataAccessor.set(step.variable, text || '');

    logger.debug('提取文本完成', {
      selector: step.selector,
      role: step.role,
      variable: step.variable,
      text: text
    });
  }

  protected async executeExtractAttribute(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    if (!step.selector && !step.role) {
      throw new Error('extractAttribute action requires selector or role field');
    }

    if (!step.attribute) {
      throw new Error('extractAttribute action requires attribute field');
    }

    if (!step.variable) {
      throw new Error('extractAttribute action requires variable field');
    }

    const element = await this.requireSingleElement(step, page, 'extractAttribute');
    const value = await element.getAttribute(step.attribute);

    // 设置为全局变量
    context.dataAccessor.set(step.variable, value || '');

    logger.debug('提取属性完成', {
      selector: step.selector,
      role: step.role,
      attribute: step.attribute,
      variable: step.variable,
      value: value
    });
  }

  protected async executeSetVariable(step: TestStep, context: ExecutionContext): Promise<void> {
    if (!step.name) {
      throw new Error('setVariable action requires name field');
    }

    if (step.value === undefined) {
      throw new Error('setVariable action requires value field');
    }

    // 解析值（可能包含变量引用）
    const resolvedValue = context.dataAccessor.resolveObject(step.value);

    // 设置为全局变量
    context.dataAccessor.set(step.name, resolvedValue);

    logger.debug('设置变量完成', {
      name: step.name,
      value: resolvedValue
    });
  }

  protected async executeUseTemplate(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    if (!step.template) {
      throw new Error('useTemplate action requires template field');
    }

    if (!context.templateResolver) {
      throw new Error('模板解析器未初始化，无法执行模板');
    }

    logger.debug('执行模板', {
      templateId: step.template,
      parameters: step.parameters
    });

    try {
      // 1. 解析模板（支持内联模板优先级）
      const resolution = context.templateResolver.resolveTemplate(step.template);
      if (!resolution.template) {
        throw new Error(`模板未找到: ${step.template}`);
      }

      // 2. 解析模板参数（先解析参数中的变量引用）
      const rawParameters = step.parameters || {};
      const resolvedParameters = context.dataAccessor.resolveObject(rawParameters);

      // 3. 验证解析后的模板参数
      const paramValidation = context.templateResolver.validateTemplateParameters(step.template, resolvedParameters);
      if (!paramValidation.isValid) {
        throw new Error(`模板参数验证失败: ${paramValidation.errors.join('; ')}`);
      }

      const template = resolution.template;

      logger.debug('模板参数解析完成', {
        templateId: step.template,
        rawParameters,
        resolvedParameters
      });

      // 4. 执行模板步骤（运行时解析）
      for (let i = 0; i < template.steps.length; i++) {
        const rawStep = template.steps[i];

        // 使用解析后的模板参数解析步骤（除了 when 条件）
        const resolvedStep = context.dataAccessor.resolveObject(rawStep, resolvedParameters);

        // 保留原始的 when 条件，避免字符串解析问题
        resolvedStep.when = rawStep.when;

        // 补充必要字段（id, name, type）以通过validateStepFormat验证
        const normalizedStep = DSLParser.normalizeStep(resolvedStep, i);

        // 递归执行步骤，传递模板参数用于条件解析
        const stepResult = await this.executeStep(normalizedStep, context, resolvedParameters);

        // 如果步骤失败，立即抛出错误（确保模板执行失败时能正确传播错误）
        if (stepResult.status === 'failed') {
          throw new Error(`模板步骤执行失败: ${stepResult.error || '未知错误'}`);
        }
      }

      logger.debug('模板执行完成', {
        templateId: step.template,
        stepsCount: template.steps.length
      });

    } catch (error) {
      logger.error('模板执行失败', {
        templateId: step.template,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  protected async executeCondition(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    const condition = step.if || step.condition;
    if (!condition) {
      throw new Error('Condition action requires if or condition field');
    }

    const isConditionTrue = this.evaluateCondition(condition, context);

    logger.debug('条件执行', {
      stepId: step.id,
      condition,
      result: isConditionTrue
    });

    if (isConditionTrue && step.then) {
      // 执行 then 分支
      for (const thenStep of step.then) {
        await this.executeStep(thenStep, context);
      }
    } else if (!isConditionTrue && step.else) {
      // 执行 else 分支
      for (const elseStep of step.else) {
        await this.executeStep(elseStep, context);
      }
    }
  }

  protected async executeConditional(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    // 向后兼容，委托给新的 executeCondition
    await this.executeCondition(step, page, context);
  }



  /**
   * 评估条件表达式
   */
  protected evaluateCondition(
    condition: string,
    context: ExecutionContext,
    templateParameters?: Record<string, any>
  ): boolean {
    try {
      // 创建执行上下文
      const evalContext = this.createEvalContext(context, templateParameters);

      // 对于条件表达式，我们需要特殊处理变量替换
      const processedCondition = this.replaceVariablesInCondition(condition, evalContext);

      logger.debug('条件表达式解析', {
        original: condition,
        processed: processedCondition,
        templateParameters,
        availableVariables: Object.keys(evalContext)
      });

      // 使用 Function 构造器创建安全的执行环境
      const func = new Function(...Object.keys(evalContext), `return ${processedCondition}`);
      const result = func(...Object.values(evalContext));

      return Boolean(result);
    } catch (error) {
      logger.warn('条件表达式执行失败', {
        condition,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 创建执行上下文
   */
  private createEvalContext(context: ExecutionContext, templateParameters?: Record<string, any>): Record<string, any> {
    const variables = context.dataAccessor.getAllVariables();
    const config = context.dataAccessor.getAllConfig();

    return {
      ...variables,
      ...(templateParameters || {}),  // 模板参数优先级最高
      config,
      // 添加一些常用的比较函数
      equals: (a: any, b: any) => a === b,
      contains: (str: string, substr: string) => str.includes(substr),
      startsWith: (str: string, prefix: string) => str.startsWith(prefix),
      endsWith: (str: string, suffix: string) => str.endsWith(suffix),
      isEmpty: (value: any) => !value || value.length === 0,
      isNotEmpty: (value: any) => value && value.length > 0
    };
  }

  /**
   * 在条件表达式中替换变量，保持正确的类型
   */
  private replaceVariablesInCondition(condition: string, evalContext: Record<string, any>): string {
    // 使用正则表达式找到所有 {{variable}} 模式
    return condition.replace(/\{\{([^}]+)\}\}/g, (match, variablePath) => {
      const trimmedPath = variablePath.trim();

      // 尝试从上下文中获取变量值
      const value = this.getNestedProperty(evalContext, trimmedPath);

      if (value !== undefined) {
        // 如果变量存在，根据类型返回适当的表示
        if (typeof value === 'string') {
          // 字符串需要引号，并且要转义内部的引号
          const escapedValue = value.replace(/"/g, '\\"');
          return `"${escapedValue}"`;
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          return String(value);  // 数字和布尔值直接转换
        } else if (value === null) {
          return 'null';
        } else {
          return JSON.stringify(value);  // 复杂对象使用 JSON
        }
      } else {
        // 如果变量不存在，返回 undefined（不加引号）
        return 'undefined';
      }
    });
  }

  /**
   * 获取嵌套属性值
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }



  protected async executeForEach(step: TestStep, page: Page, context: ExecutionContext): Promise<void> {
    const { items, steps, item: itemVar = 'item', index: indexVar = 'index' } = step as any;

    if (!items || !steps) {
      throw new Error('forEach action requires items and steps properties');
    }

    // 解析 items - 可能是数组或变量路径
    let itemsArray: any[];
    if (Array.isArray(items)) {
      itemsArray = items;
    } else if (typeof items === 'string') {
      // 从上下文中获取变量值
      itemsArray = context.dataAccessor.resolve(items);
      if (!Array.isArray(itemsArray)) {
        throw new Error(`Variable ${items} is not an array`);
      }
    } else {
      throw new Error('items must be an array or variable path string');
    }

    logger.info(`Executing forEach with ${itemsArray.length} items`);

    // 遍历每个项目
    for (let i = 0; i < itemsArray.length; i++) {
      const currentItem = itemsArray[i];

      logger.info(`Processing forEach item ${i + 1}/${itemsArray.length}:`, currentItem);

      // 设置当前项和索引变量到数据访问器
      const originalItemValue = context.dataAccessor.get(itemVar);
      const originalIndexValue = context.dataAccessor.get(indexVar);

      context.dataAccessor.set(itemVar, currentItem);
      context.dataAccessor.set(indexVar, i);

      try {
        // 执行步骤
        for (const subStep of steps) {
          await this.executeStepAction(subStep, context);
        }
      } finally {
        // 恢复原始变量值
        if (originalItemValue !== undefined) {
          context.dataAccessor.set(itemVar, originalItemValue);
        }
        if (originalIndexValue !== undefined) {
          context.dataAccessor.set(indexVar, originalIndexValue);
        }
      }
    }

    logger.info('forEach execution completed');
  }
}
