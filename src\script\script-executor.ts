/**
 * 脚本执行器
 * 执行注册的JavaScript脚本
 */

import type { 
  ScriptExecutionContext, 
  ScriptExecutionResult, 
  ScriptDefinition 
} from './types';
import { ScriptRegistry } from './script-registry';
import { logger } from '../utils/logger';

export class ScriptExecutor {
  constructor(private registry: ScriptRegistry) {}

  /**
   * 执行脚本
   */
  async executeScript(
    scriptName: string,
    parameters: any = {},
    context: ScriptExecutionContext,
    timeout: number = 30000
  ): Promise<ScriptExecutionResult> {
    const startTime = Date.now();

    try {
      logger.info('开始执行脚本', { 
        scriptName, 
        parameters: this.sanitizeParameters(parameters),
        timeout 
      });

      // 获取脚本定义
      const script = this.registry.getScript(scriptName);

      if (!script) {
        const error = `脚本不存在: ${scriptName}`;
        logger.error(error);
        return {
          success: false,
          error,
          duration: Date.now() - startTime
        };
      }

      // 验证参数
      const paramValidation = this.registry.validateScriptParameters(scriptName, parameters);
      if (!paramValidation.isValid) {
        const error = `参数验证失败: ${paramValidation.errors.map(e => e.message).join('; ')}`;
        logger.error('脚本参数验证失败', { scriptName, errors: paramValidation.errors });
        return {
          success: false,
          error,
          duration: Date.now() - startTime
        };
      }

      // 准备执行参数（合并默认值）
      const finalParameters = this.prepareParameters(script, parameters);

      // 创建执行上下文
      const executionContext = this.createExecutionContext(context, scriptName);

      // 执行脚本（带超时控制）
      const result = await this.executeWithTimeout(
        script,
        finalParameters,
        executionContext,
        timeout
      );

      const duration = Date.now() - startTime;

      logger.info('脚本执行成功', { 
        scriptName, 
        duration: `${duration}ms`,
        hasResult: result !== undefined
      });

      return {
        success: true,
        result,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      logger.error('脚本执行失败', { 
        scriptName, 
        error: errorMessage,
        duration: `${duration}ms`
      });

      return {
        success: false,
        error: errorMessage,
        duration
      };
    }
  }

  /**
   * 带超时的脚本执行
   */
  private async executeWithTimeout(
    script: ScriptDefinition,
    parameters: any,
    context: ScriptExecutionContext,
    timeout: number
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        reject(new Error(`脚本执行超时 (${timeout}ms): ${script.name}`));
      }, timeout);

      try {
        debugger;
        // 执行脚本
        const result = await script.execute(parameters, context);
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * 准备执行参数（合并默认值）
   */
  private prepareParameters(script: ScriptDefinition, parameters: any): any {
    const finalParameters = { ...parameters };

    // 应用默认值
    if (script.metadata.parameters) {
      for (const [paramName, paramDef] of Object.entries(script.metadata.parameters)) {
        if (finalParameters[paramName] === undefined && paramDef.default !== undefined) {
          finalParameters[paramName] = paramDef.default;
        }
      }
    }

    return finalParameters;
  }

  /**
   * 创建脚本执行上下文
   */
  private createExecutionContext(
    baseContext: ScriptExecutionContext,
    scriptName: string
  ): ScriptExecutionContext {
    return {
      ...baseContext,
      // 为脚本创建专用的logger
      logger: {
        ...baseContext.logger,
        info: (message: string, meta?: any) => 
          baseContext.logger.info(`[Script:${scriptName}] ${message}`, meta),
        error: (message: string, meta?: any) => 
          baseContext.logger.error(`[Script:${scriptName}] ${message}`, meta),
        warn: (message: string, meta?: any) => 
          baseContext.logger.warn(`[Script:${scriptName}] ${message}`, meta),
        debug: (message: string, meta?: any) => 
          baseContext.logger.debug(`[Script:${scriptName}] ${message}`, meta),
      }
    };
  }

  /**
   * 清理敏感参数用于日志记录
   */
  private sanitizeParameters(parameters: any): any {
    if (!parameters || typeof parameters !== 'object') {
      return parameters;
    }

    const sanitized = { ...parameters };
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth'];

    for (const key of Object.keys(sanitized)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '***';
      }
    }

    return sanitized;
  }

  /**
   * 获取脚本执行统计
   */
  getExecutionStatistics() {
    // 这里可以添加执行统计功能，遵循YAGNI原则暂时简化
    return {
      registeredScripts: this.registry.getScriptNames().length,
      availableScripts: this.registry.getScriptNames()
    };
  }
}
