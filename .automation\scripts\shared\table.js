/**
 * 表格操作脚本
 * 提供表格行的查找、编辑和删除功能
 */

// 常量定义
const TABLE_CONSTANTS = {
  // 按钮文本
  NEXT_PAGE: '下一页',
  PREVIOUS_PAGE: '上一页',
  OPERATION: '操作',
  EDIT: '修改',
  EDIT2: '编辑',
  DELETE: '删除',
  DETAIL: '详情',
  VIEW: '查看',
  CONFIRM: '确定',

  // 角色定义
  BUTTON_ROLE: 'button',
  LISTITEM_ROLE: 'listitem',
  SPINBUTTON_ROLE: 'spinbutton',
  ROW_ROLE: 'row',
  CELL_ROLE: 'cell',
  DIALOG_ROLE: 'dialog',

  // 等待时间
  WAIT_TIMEOUT: 1000,
  SHORT_WAIT: 500,
  DIALOG_WAIT: 2000,

  // 其他
  PAGE_LABEL: '页'
};

// 全局缓存列名
let columnNamesCache = null;

// 操作类型判断函数
function isEditOperation(operation) {
  return operation === 'edit';
}

function isDeleteOperation(operation) {
  return operation === 'delete';
}

function isViewOperation(operation) {
  return operation === 'view' || operation === 'detail';
}

// 获取按钮名称数组
function getEditButtonNames() {
  return [TABLE_CONSTANTS.EDIT, TABLE_CONSTANTS.EDIT2];
}

function getDeleteButtonNames() {
  return [TABLE_CONSTANTS.DELETE];
}

function getViewButtonNames() {
  return [TABLE_CONSTANTS.VIEW, TABLE_CONSTANTS.DETAIL];
}

export default async function tableOperations(params, context) {
  const { actionType, fields, timeout = TABLE_CONSTANTS.WAIT_TIMEOUT } = params;
  const { page, logger } = context;

  if (!actionType) {
    return {
      success: false,
      message: '必须指定 actionType 参数'
    };
  }

  logger.info('开始表格操作', { actionType, fieldsCount: fields?.length, timeout });

  try {
    let result;

    // 根据操作类型执行相应的操作
    if (actionType === 'getTableTotal') {
      result = await getTableTotal(page, logger);
    } else if (actionType === 'findRows') {
      result = await findRows(page, fields, logger);
    } else if (isEditOperation(actionType)) {
      result = await performRowOperationWithValidation(page, fields, getEditButtonNames(), false);
    } else if (isDeleteOperation(actionType)) {
      result = await performRowOperationWithValidation(page, fields, getDeleteButtonNames(), true);
    } else if (isViewOperation(actionType)) {
      result = await performRowOperationWithValidation(page, fields, getViewButtonNames(), false);
    } else {
      // 自定义操作
      result = await performRowOperationWithValidation(page, fields, [actionType], false);
    }

    const success = result === true || (typeof result === 'object' && result.success);
    const message = success ? `${actionType} 操作成功` : `${actionType} 操作失败`;

    logger.info('表格操作完成', { actionType, success });

    return {
      success,
      message,
      data: typeof result === 'object' ? result : undefined
    };

  } catch (error) {
    const errorMessage = error.message || '未知错误';
    logger.error('表格操作失败', { actionType, error: errorMessage });

    return {
      success: false,
      message: `${actionType} 操作失败: ${errorMessage}`,
      error: errorMessage
    };
  }
}

async function getTableTotal(page, logger) {
  try {
    const total = await page.evaluate(() => {
      const totalElement = document.querySelector('.el-pagination__total');
      if (!totalElement) {
        throw new Error('未找到分页总数元素');
      }
      const match = totalElement.textContent.match(/\d+/);
      if (!match) {
        throw new Error('无法解析总数');
      }
      return +match[0];
    });

    return {
      success: true,
      action: 'getTableTotal',
      total: total
    };
  } catch (error) {
    if (logger) {
      logger.error('获取表格总数失败', { error: error.message });
    }
    return {
      success: false,
      action: 'getTableTotal',
      error: error.message
    };
  }
}

// 查找行操作
async function findRows(page, fields, logger) {
  try {
    logger.info('开始查找表格行', { fieldsCount: fields?.length });

    const foundRows = await findRowInAllPages(page, fields);

    return {
      success: true,
      action: 'findRows',
      foundCount: foundRows.length,
      rows: foundRows
    };
  } catch (error) {
    return {
      success: false,
      action: 'findRows',
      error: error.message
    };
  }
}

// 带验证的行操作方法
async function performRowOperationWithValidation(page, fields, buttonNames, needsConfirmation = false) {
  try {
    if (needsConfirmation) {
      // 删除操作需要特殊处理（保留验证逻辑）
      return await deleteRowOperation(page, fields);
    } else {
      // 其他操作使用通用方法
      return await performRowOperation(page, fields, buttonNames, needsConfirmation);
    }
  } catch (error) {
    console.log(`行操作失败: ${error.message}`);
    throw error; // 抛出异常而不是返回false
  }
}

// 翻页控制函数
async function goToNextPage(page) {
  const nextButton = await page.getByRole(TABLE_CONSTANTS.BUTTON_ROLE, { name: TABLE_CONSTANTS.NEXT_PAGE });
  if (await nextButton.isEnabled()) {
    await nextButton.click();
    await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);
    return true;
  }
  throw new Error('下一页按钮不可用或不存在');
}

async function goToPreviousPage(page) {
  const prevButton = await page.getByRole(TABLE_CONSTANTS.BUTTON_ROLE, { name: TABLE_CONSTANTS.PREVIOUS_PAGE });
  if (await prevButton.isEnabled()) {
    await prevButton.click();
    await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);
    return true;
  }
  throw new Error('上一页按钮不可用或不存在');
}

async function goToPage(page, pageNumber) {
  // 通过页码列表直接点击
  const pageItem = await page.getByRole(TABLE_CONSTANTS.LISTITEM_ROLE, { name: `第 ${pageNumber} 页` });
  if (await pageItem.isVisible()) {
    await pageItem.click();
    await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);
    return true;
  }

  // 如果页码不在列表中，使用跳转输入框
  const pageInput = await page.getByRole(TABLE_CONSTANTS.SPINBUTTON_ROLE, { name: TABLE_CONSTANTS.PAGE_LABEL });
  await pageInput.fill(pageNumber.toString());
  await page.keyboard.press('Enter');
  await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);
  return true;
}

// 获取当前页面信息
async function getCurrentPageInfo(page) {
  const totalText = await page.locator('text=共').textContent();
  const currentPageInput = await page.getByRole(TABLE_CONSTANTS.SPINBUTTON_ROLE, { name: TABLE_CONSTANTS.PAGE_LABEL });
  const currentPage = await currentPageInput.inputValue();

  return {
    total: totalText,
    currentPage: parseInt(currentPage),
  };
}

// 获取真实的列名（从表头提取）
async function getColumnNames(page) {
  if (columnNamesCache) {
    return columnNamesCache;
  }

  const headerRow = await page.getByRole(TABLE_CONSTANTS.ROW_ROLE).first();
  const headerCells = headerRow.getByRole(TABLE_CONSTANTS.CELL_ROLE);
  const cellCount = await headerCells.count();

  const columnNames = [];
  for (let i = 0; i < cellCount; i++) {
    const cell = headerCells.nth(i);
    const cellText = await cell.textContent();
    columnNames.push(cellText?.trim() || `column${i}`);
  }

  columnNamesCache = columnNames;
  console.log('获取到的列名:', columnNames);
  return columnNames;
}

// 在所有页面查找指定行
// searchCriteria 格式: [
//   {
//     column: '名称',
//     value: 'XH001',
//     exact: true,
//   },
//   {
//     column: '编码',
//     value: 'L0101',
//     anyOf: ['名称', '编码'],
//     exact: false,
//   }
// ]
async function findRowInAllPages(page, searchCriteria) {
  // 记录当前页面
  const originalPageInfo = await getCurrentPageInfo(page);
  const originalPage = originalPageInfo.currentPage;

  // 从第一页开始查找
  await goToPage(page, 1);
  await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);

  let currentPage = 1;
  const foundRows = [];

  while (true) {
    console.log(`正在查找第 ${currentPage} 页...`);

    // 在当前页面查找匹配的行
    const matchingRows = await findRowsInCurrentPage(page, searchCriteria);

    if (matchingRows.length > 0) {
      matchingRows.forEach(row => {
        foundRows.push({
          ...row,
          pageNumber: currentPage
        });
      });
    }

    // 尝试翻到下一页
    const hasNextPage = await goToNextPage(page);
    if (!hasNextPage) {
      break; // 没有下一页了
    }

    currentPage++;
    await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);
  }

  // 返回原始页面
  if (originalPage !== currentPage) {
    await goToPage(page, originalPage);
    await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);
  }

  console.log(`查找完成，共找到 ${foundRows.length} 条匹配记录`);
  return foundRows;
}

// 在当前页面查找匹配的行
async function findRowsInCurrentPage(page, searchCriteria) {
  const matchingRows = [];

  // 获取真实的列名
  const columnNames = await getColumnNames(page);

  // 获取所有数据行（排除表头）
  const allRows = await page.getByRole(TABLE_CONSTANTS.ROW_ROLE);
  const rowCount = await allRows.count();

  // 从第二行开始（跳过表头）
  for (let i = 1; i < rowCount; i++) {
    const row = allRows.nth(i);
    const cells = row.getByRole(TABLE_CONSTANTS.CELL_ROLE);
    const cellCount = await cells.count();

    // 提取行数据，使用真实的列名
    const rowData = {};
    for (let j = 0; j < cellCount && j < columnNames.length; j++) {
      const cell = cells.nth(j);
      const cellText = await cell.textContent();
      const columnName = columnNames[j];
      rowData[columnName] = cellText?.trim() || '';
    }

    // 检查是否匹配搜索条件
    if (isRowMatching(rowData, searchCriteria)) {
      matchingRows.push({
        rowIndex: i - 1, // 实际数据行索引（不包括表头）
        rowData: rowData
      });
    }
  }

  return matchingRows;
}

// 检查行数据是否匹配搜索条件
// searchCriteria 格式: [
//   {
//     column: '名称',
//     value: 'XH001',
//     exact: true,
//   },
//   {
//     column: '编码',
//     value: 'L0101',
//     anyOf: ['名称', '编码'],
//     exact: false,
//   }
// ]
function isRowMatching(rowData, searchCriteria) {
  // 所有条件都必须匹配
  for (const criteria of searchCriteria) {
    const { column, value, exact = true, anyOf } = criteria;

    if (anyOf && Array.isArray(anyOf)) {
      // anyOf: 检查多个列中是否有任意一个匹配
      let anyMatch = false;
      for (const columnName of anyOf) {
        const actualValue = rowData[columnName] || '';
        if (isValueMatching(actualValue, value, exact)) {
          anyMatch = true;
          break;
        }
      }
      if (!anyMatch) {
        return false;
      }
    } else {
      // 单列匹配
      const actualValue = rowData[column] || '';
      if (!isValueMatching(actualValue, value, exact)) {
        return false;
      }
    }
  }

  return true;
}

// 值匹配辅助方法
// exact: true = 精确匹配 (equal), false = 包含匹配 (contains)
// 默认区分大小写
function isValueMatching(actualValue, expectedValue, exact = true) {
  if (exact) {
    // 精确匹配 (equal)
    return actualValue === expectedValue;
  } else {
    // 包含匹配 (contains)
    return actualValue.includes(expectedValue);
  }
}

// 公共的行操作方法
async function performRowOperation(page, searchCriteria, buttonNames, needsConfirmation = false) {
  console.log(`查找并执行行操作: ${buttonNames.join(' 或 ')}`);

  // 查找匹配的行
  const foundRows = await findRowInAllPages(page, searchCriteria);

  if (foundRows.length === 0) {
    throw new Error('未找到匹配的行');
  }

  // 操作第一个匹配的行
  const targetRow = foundRows[0];
  console.log(`找到匹配行，导航到第 ${targetRow.pageNumber} 页`);

  // 导航到目标页面
  await goToPage(page, targetRow.pageNumber);
  await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);

  // 选中目标行
  const rows = await page.getByRole(TABLE_CONSTANTS.ROW_ROLE);
  const targetRowElement = rows.nth(targetRow.rowIndex + 1); // 加1是因为要跳过表头

  // 在该行中查找操作按钮
  const operationButton = targetRowElement.getByRole(TABLE_CONSTANTS.BUTTON_ROLE, { name: TABLE_CONSTANTS.OPERATION });
  if (await operationButton.isVisible()) {
    await operationButton.hover();
    await page.waitForTimeout(TABLE_CONSTANTS.SHORT_WAIT);

    // 尝试点击指定的按钮（按优先级顺序）
    for (const buttonName of buttonNames) {
      try {
        const targetButton = await page.getByRole(TABLE_CONSTANTS.BUTTON_ROLE, { name: buttonName });
        if (await targetButton.isVisible()) {
          await targetButton.click();
          await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);
          console.log(`已点击 ${buttonName} 按钮`);

          // 如果需要确认，处理确认对话框
          if (needsConfirmation) {
            await handleConfirmationDialog(page);
          }

          return true;
        }
      } catch (error) {
        console.log(`尝试点击 ${buttonName} 按钮失败: ${error.message}`);
        continue;
      }
    }

    throw new Error(`未找到任何可用的操作按钮: ${buttonNames.join(', ')}`);
  }

  throw new Error('未找到操作按钮');
}

// 处理确认对话框
async function handleConfirmationDialog(page) {
  try {
    // 等待可能出现的确认对话框
    const confirmDialog = page.getByRole(TABLE_CONSTANTS.DIALOG_ROLE);
    await confirmDialog.waitFor({ timeout: TABLE_CONSTANTS.DIALOG_WAIT });

    if (await confirmDialog.isVisible()) {
      console.log('检测到确认对话框，点击确定按钮');

      // 在对话框中查找确定按钮
      const confirmButton = confirmDialog.getByRole(TABLE_CONSTANTS.BUTTON_ROLE, { name: TABLE_CONSTANTS.CONFIRM });
      if (await confirmButton.isVisible()) {
        await confirmButton.click();
        console.log('已点击确定按钮');
      } else {
        // 如果在对话框内找不到确定按钮，尝试在页面级别查找
        const globalConfirmButton = page.getByRole(TABLE_CONSTANTS.BUTTON_ROLE, { name: TABLE_CONSTANTS.CONFIRM });
        if (await globalConfirmButton.isVisible()) {
          await globalConfirmButton.click();
          console.log('已点击全局确定按钮');
        } else {
          throw new Error('未找到确定按钮');
        }
      }

      // 等待对话框消失
      await confirmDialog.waitFor({ state: 'hidden', timeout: TABLE_CONSTANTS.DIALOG_WAIT });
    }
  } catch (error) {
    console.log('未检测到确认对话框，可能直接操作成功');
  }
}

// 编辑行操作的具体实现（重构为使用公共方法）
async function editRowOperation(page, searchCriteria) {
  console.log('查找并编辑指定行...');
  return await performRowOperation(page, searchCriteria, [TABLE_CONSTANTS.EDIT, TABLE_CONSTANTS.EDIT2]);
}

// 删除行操作的具体实现（重构为使用公共方法，但保留验证逻辑）
async function deleteRowOperation(page, searchCriteria) {
  console.log('查找并删除指定行...');
  console.log("searchCriteria", JSON.stringify(searchCriteria, null, 2));

  // 1. 获取删除前的数据总量（可选，用于验证）
  let totalBeforeDelete = null;
  try {
    const beforeDeleteResult = await getTableTotal(page, console);
    if (beforeDeleteResult.success) {
      totalBeforeDelete = beforeDeleteResult.total;
      console.log(`删除前数据总量: ${totalBeforeDelete}`);
    } else {
      console.log('获取删除前数据总量失败，将跳过验证:', beforeDeleteResult.error);
    }
  } catch (error) {
    console.log('获取删除前数据总量异常，将跳过验证:', error.message);
  }

  // 2. 执行删除操作
  const deleteResult = await performRowOperation(page, searchCriteria, [TABLE_CONSTANTS.DELETE], true);

  // 3. 如果有删除前总数，验证删除是否成功
  if (totalBeforeDelete !== null && deleteResult) {
    try {
      // 等待删除操作完成
      await page.waitForTimeout(TABLE_CONSTANTS.WAIT_TIMEOUT);

      const afterDeleteResult = await getTableTotal(page, console);
      if (afterDeleteResult.success) {
        const totalAfterDelete = afterDeleteResult.total;
        console.log(`删除后数据总量: ${totalAfterDelete}`);

        if (totalAfterDelete < totalBeforeDelete) {
          console.log('删除成功（已验证）');
          return true;
        } else {
          console.log('删除可能失败，数据总量未减少');
          return false;
        }
      } else {
        console.log('获取删除后数据总量失败，无法验证删除结果:', afterDeleteResult.error);
        // 即使无法验证，如果操作执行成功，也返回 true
        return deleteResult;
      }
    } catch (error) {
      console.log('验证删除结果时发生异常:', error.message);
      // 即使验证失败，如果操作执行成功，也返回 true
      return deleteResult;
    }
  }

  return deleteResult;
}

// 脚本元数据
export const metadata = {
  name: "table-operations",
  description: "表格操作工具，支持查找、编辑、删除、查看等表格行操作",
  version: "2.0.0",
  parameters: {
    actionType: {
      type: "string",
      required: false,
      description: "操作类型: edit, delete, view, detail, findRows, getTableTotal"
    },
    actionName: {
      type: "string",
      required: false,
      description: "具体操作名称（优先级高于 actionType），如：修改、删除、查看、详情等"
    },
    fields: {
      type: "array",
      required: true,
      description: "搜索条件数组，每个元素包含 column, value, exact, anyOf 等字段",
      items: {
        type: "object",
        properties: {
          column: {
            type: "string",
            description: "列名"
          },
          value: {
            type: "string",
            description: "搜索值"
          },
          exact: {
            type: "boolean",
            default: true,
            description: "是否精确匹配，true=精确匹配，false=包含匹配"
          },
          anyOf: {
            type: "array",
            description: "可选的多列匹配，在指定的列中任意一个匹配即可",
            items: {
              type: "string"
            }
          }
        }
      }
    },
    timeout: {
      type: "number",
      default: 1000,
      description: "操作超时时间(毫秒)"
    }
  },
  returns: {
    success: {
      type: "boolean",
      description: "操作是否成功"
    },
    message: {
      type: "string",
      description: "操作结果消息"
    },
    data: {
      type: "object",
      description: "操作结果数据（可选）",
      properties: {
        foundCount: {
          type: "number",
          description: "找到的行数（仅findRows返回）"
        },
        rows: {
          type: "array",
          description: "找到的行数据（仅findRows返回）"
        },
        total: {
          type: "number",
          description: "表格总行数（仅getTableTotal返回）"
        }
      }
    }
  },
  examples: [
    {
      description: "查找名称为'XH001'的行",
      params: {
        actionType: "findRows",
        fields: [
          {
            column: "名称",
            value: "XH001",
            exact: true
          }
        ]
      }
    },
    {
      description: "编辑包含'L0101'编码的行",
      params: {
        actionType: "edit",
        fields: [
          {
            column: "编码",
            value: "L0101",
            exact: false
          }
        ]
      }
    },
    {
      description: "删除在名称或编码列中包含'test'的行",
      params: {
        actionType: "delete",
        fields: [
          {
            value: "test",
            exact: false,
            anyOf: ["名称", "编码"]
          }
        ]
      }
    },
    {
      description: "获取表格总行数",
      params: {
        actionType: "getTableTotal"
      }
    },
    {
      description: "查看包含'L0101'编码的行详情",
      params: {
        actionType: "view",
        fields: [
          {
            column: "编码",
            value: "L0101",
            exact: false
          }
        ]
      }
    },
    {
      description: "使用具体操作名称进行编辑",
      params: {
        actionName: "修改",
        fields: [
          {
            column: "名称",
            value: "测试数据",
            exact: true
          }
        ]
      }
    },
    {
      description: "使用具体操作名称进行自定义操作",
      params: {
        actionName: "审核",
        fields: [
          {
            column: "状态",
            value: "待审核",
            exact: true
          }
        ]
      }
    }
  ],
  tags: ["table", "ui", "interaction", "search", "edit", "delete"]
};